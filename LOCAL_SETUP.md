# ProX Local Development Setup

This guide will help you set up the ProX Drupal 7 website locally using Docker.

## Prerequisites

- Docker Desktop installed and running
- The database export file in the `db/` directory

## Quick Start

1. **Start the development environment:**
   ```bash
   ./start-local.sh start
   ```

2. **Access the website:**
   - Website: http://localhost:8080
   - Admin login: Use your existing Drupal admin credentials

3. **Database access:**
   - Host: localhost
   - Port: 3307
   - Database: prox_local
   - Username: root
   - Password: Nisila@123

## Management Commands

Use the `start-local.sh` script to manage your development environment:

```bash
# Start containers
./start-local.sh start

# Stop containers
./start-local.sh stop

# Restart containers
./start-local.sh restart

# Rebuild containers (if you make changes to Dockerfile)
./start-local.sh rebuild

# View logs
./start-local.sh logs

# Check container status
./start-local.sh status
```

## Manual Docker Commands

If you prefer using Docker commands directly:

```bash
# Start containers
docker-compose up -d

# Stop containers
docker-compose down

# View logs
docker-compose logs -f

# Rebuild and start
docker-compose up --build -d
```

## Technology Stack

- **PHP**: 7.4 (compatible with Drupal 7)
- **Apache**: 2.4 with mod_rewrite and headers enabled
- **MySQL**: 5.7
- **Drupal**: 7.x

## File Structure

- `Dockerfile` - Web server configuration
- `docker-compose.yml` - Multi-container setup
- `start-local.sh` - Management script
- `db/` - Database export files
- `sites/default/settings.php` - Drupal configuration

## Troubleshooting

1. **Port conflicts**: If port 8080 or 3307 are in use, modify `docker-compose.yml`
2. **Database issues**: Check logs with `./start-local.sh logs`
3. **File permissions**: The containers automatically set proper permissions

## Development Workflow

1. Make changes to your code
2. Refresh the browser (no restart needed for code changes)
3. For configuration changes, restart with `./start-local.sh restart`

## Stopping the Environment

When you're done developing:
```bash
./start-local.sh stop
```

This will stop all containers but preserve your data.
