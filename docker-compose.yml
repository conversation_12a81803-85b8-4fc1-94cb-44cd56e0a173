services:
  web:
    build: .
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
    depends_on:
      - db
    environment:
      - APACHE_DOCUMENT_ROOT=/var/www/html

  db:
    image: mysql:5.7
    environment:
      MYSQL_ROOT_PASSWORD: <PERSON><PERSON><PERSON>@123
      MYSQL_DATABASE: prox_local
      MYSQL_USER: prox_user
      MYSQL_PASSWORD: prox_pass
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./db:/docker-entrypoint-initdb.d

volumes:
  mysql_data:
